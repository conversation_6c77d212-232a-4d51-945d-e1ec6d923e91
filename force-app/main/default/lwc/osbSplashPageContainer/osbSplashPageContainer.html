<template>
    <div class="splash-page-header-background">
        <img src={headerBackground} alt="Shield background">
    </div>
    <div class="splash-page-wrapper">
        <div class="splash-page-header">
        <op-detailed-product-card
        lwc:external
        title={apiHeading}
        description={apiSubheading}
      >
        <img
          slot="image"
          src={image}
          alt="Portfolio Dashboard"
          style="width: 100%; height: 100%; object-fit: cover;"
        />
        <op-button lwc:external slot="actions" onclick={handleGetInTouchClick}>GET IN TOUCH</op-button>
      </op-detailed-product-card>

        </div>
        <div class="splash-page-value-prop">
            <c-osb-value-propositions
                heading={apiHeading}
                value-propositions={benefitsList}>
            </c-osb-value-propositions>
        </div>
        <div class="splash-page-cta-section">
            <c-osbapi-api-catalogue
                product-id={recordId}>
            </c-osbapi-api-catalogue>
        </div>
        <template lwc:if={tabObject}>
            <div class="splash-pagetab-section">
                <c-osb-splash-tab-body
                    data={tabObject}>
                </c-osb-splash-tab-body>
            </div>
        </template>
    </div>

    <!-- Get in Touch Modal -->
    <template lwc:if={showGetInTouchModal}>
        <div class="modal-backdrop" onclick={handleCloseModal}>
            <div class="get-in-touch-modal" onclick={handleModalClick}>
                <div class="modal-header">
                    <h2>Get in touch</h2>
                    <button class="close-button" onclick={handleCloseModal}>
                        <lightning-icon icon-name="utility:close" size="x-small"></lightning-icon>
                    </button>
                </div>
                <div class="modal-content">
                    <p class="modal-description">
                        Once you submit this form, the API will be automatically added to your dashboard.
                    </p>

                    <form class="contact-form">
                        <op-input
                            lwc:external
                            label="Name and Surname"
                            placeholder="John Dlamini"
                            name="fullname"
                            required
                            value={formData.name}
                            onupdatevalue={handleInputChange}
                        ></op-input>

                        <op-input
                            lwc:external
                            label="Email"
                            placeholder="<EMAIL>"
                            type="email"
                            name="email"
                            required
                            value={formData.email}
                            onupdatevalue={handleInputChange}
                        ></op-input>
                        <op-input
                            lwc:external
                            label="Contact number"
                            placeholder="+27 724355032"
                            name="phone"
                            type="tel"
                            required
                            value={formData.phone}
                            onupdatevalue={handleInputChange}
                        ></op-input>

                        <op-input
                            lwc:external
                            label="Solution name"
                            placeholder="Authentifi"
                            name="solution"
                            value={formData.solution}
                            onupdatevalue={handleInputChange}
                        ></op-input>

                        <div class="form-group">
                            <label for="requestType">Type of request</label>
                            <select
                                id="requestType"
                                onchange={handleInputChange}
                                data-field="requestType"
                            >
                                <option value="">Get a call back</option>
                                <option value="demo">Book a demo</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="contactMethod">Preferred contact method</label>
                            <select
                                id="contactMethod"
                                onchange={handleInputChange}
                                data-field="contactMethod"
                            >
                                <option value="">Select an option</option>
                                <option value="phone">Phone</option>
                                <option value="email">Email</option>
                                <option value="teams">Teams</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="messageInput">Tell us more about how you would like to use this solution</label>
                            <textarea
                                id="messageInput"
                                placeholder="Placeholder"
                                onchange={handleInputChange}
                                data-field="message"
                                rows="4"
                            >{formData.message}</textarea>
                        </div>

                        <op-checkbox
                            lwc:external
                            onupdatechecked={onUpdateChecked}
                            name="privacyConsent"
                            >I acknowledge and consent to the processing of my personal information by Standard Bank and/or relevant third-party providers, in line with Standard Bank's Privacy Statement.
                        </op-checkbox>
                        <op-button 
                            lwc:external kind="primary" 
                            onclick={handleSubmit}>SUBMIT
                        </op-button>
                    </form>
                </div>
            </div>
        </div>
    </template>
</template>