<template>
    <div class="splash-page-header-background">
        <img src={headerBackground} alt="Shield background">
    </div>
    <div class="splash-page-wrapper">
        <div class="splash-page-header">
            <op-detailed-product-card
                lwc:external
                title={apiHeadings}
                description={apiSubheading}
            >
                <img
                slot="image"
                src={image}
                alt="Portfolio Dashboard"
                style="width: 100%; height: 100%; object-fit: cover;"
                />
            <op-button lwc:external slot="actions">GET IN TOUCH</op-button>
      </op-detailed-product-card>  
        </div>
        <div class="splash-page-value-prop">
            <c-osb-value-propositions
                heading={apiHeading}
                value-propositions={benefitsList}>
            </c-osb-value-propositions>
        </div>
        <div class="splash-page-cta-section">
            <c-osbapi-api-catalogue
                product-id={recordId}>
            </c-osbapi-api-catalogue>
        </div>
        <template lwc:if={tabObject}>
            <div class="splash-pagetab-section">
                <c-osb-splash-tab-body
                    data={tabObject}>
                </c-osb-splash-tab-body>
            </div>
        </template>
    </div>
    <c-osb-get-in-touch-modal
        show={showGetInTouchModal}
        onclose={handleCloseModal}
        onsubmit={handleModalSubmit}>
    </c-osb-get-in-touch-modal>
</template>